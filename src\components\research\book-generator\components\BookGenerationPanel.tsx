import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { 
  Bot, 
  FileText, 
  Clock, 
  CheckCircle, 
  Download, 
  Edit, 
  Loader2, 
  BookOpen,
  AlertCircle,
  BarChart3
} from "lucide-react";
import { GeneratedChapter, BookMetadata, Citation } from '../types';
import { useBookContextStore } from '../stores/book-context.store';

interface BookGenerationPanelProps {
  generatedChapters: GeneratedChapter[];
  generatedSections: GeneratedChapter[];
  isGenerating: boolean;
  bookMetadata: BookMetadata;
  allCitations?: Citation[];
  chapterCitations?: Record<string, string[]>;
}

export const BookGenerationPanel: React.FC<BookGenerationPanelProps> = ({
  generatedChapters,
  generatedSections,
  isGenerating,
  bookMetadata,
  allCitations = [],
  chapterCitations = {}
}) => {
  const { generationProgress, currentlyGenerating } = useBookContextStore();
  
  const allContent = [...generatedSections, ...generatedChapters].sort((a, b) => a.order - b.order);
  const completedContent = allContent.filter(item => item.status === 'completed');
  const totalWordCount = completedContent.reduce((sum, item) => sum + (item.wordCount || 0), 0);
  
  const getProgressForItem = (itemId: string): number => {
    return generationProgress[itemId] || 0;
  };

  const getStatusIcon = (status: string, itemId: string) => {
    if (currentlyGenerating === itemId) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'generating':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string, itemId: string) => {
    if (currentlyGenerating === itemId) {
      return (
        <Badge className="bg-blue-500 hover:bg-blue-600 flex items-center gap-2">
          <Loader2 className="h-3 w-3 animate-spin" />
          Generating
        </Badge>
      );
    }
    
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-2">
            <CheckCircle className="h-3 w-3" />
            Complete
          </Badge>
        );
      case 'generating':
        return (
          <Badge className="bg-blue-500 hover:bg-blue-600 flex items-center gap-2">
            <Loader2 className="h-3 w-3 animate-spin" />
            Generating
          </Badge>
        );
      case 'error':
        return (
          <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-2">
            <AlertCircle className="h-3 w-3" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
    }
  };

  return (
    <div className="grid lg:grid-cols-2 gap-8">
      {/* Generation Progress */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            Generation Progress
          </CardTitle>
          <CardDescription className="text-lg">
            {completedContent.length} of {allContent.length} sections completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Overall Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{Math.round((completedContent.length / allContent.length) * 100)}%</span>
              </div>
              <Progress value={(completedContent.length / allContent.length) * 100} className="h-2" />
            </div>

            {/* Book Statistics */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalWordCount.toLocaleString()}</div>
                <div className="text-sm text-gray-600">Words Generated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{allCitations.length}</div>
                <div className="text-sm text-gray-600">Citations Found</div>
              </div>
            </div>

            {/* Individual Item Progress */}
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-3">
                {allContent.map((item) => {
                  const progress = getProgressForItem(item.id);
                  const isCurrentlyGenerating = currentlyGenerating === item.id;
                  
                  return (
                    <div key={item.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-xl bg-white hover:shadow-sm transition-all duration-200">
                      <div className="flex items-center gap-3 flex-1">
                        <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100">
                          {getStatusIcon(item.status, item.id)}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">{item.title}</div>
                          {isCurrentlyGenerating && progress > 0 && (
                            <div className="mt-1">
                              <Progress value={progress} className="h-1" />
                            </div>
                          )}
                          {item.wordCount && (
                            <div className="text-xs text-gray-500 mt-1">
                              {item.wordCount.toLocaleString()} words
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {chapterCitations[item.id]?.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {chapterCitations[item.id].length} citations
                          </Badge>
                        )}
                        {getStatusBadge(item.status, item.id)}
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>

      {/* Generated Content Preview */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-green-500 to-purple-500 rounded-full"></div>
            Generated Content
          </CardTitle>
          <CardDescription className="text-lg">
            {completedContent.length === 0
              ? "Content will appear here as it's generated"
              : `Preview of your ${bookMetadata.title}`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px] pr-4">
            <div className="space-y-6">
              {completedContent.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[400px] text-center">
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-16 w-16 text-blue-500 animate-spin mb-6" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">Generating Your Book</h3>
                      <p className="text-gray-500 max-w-md">
                        Our AI is carefully crafting each chapter and section with context awareness. This may take several minutes...
                      </p>
                    </>
                  ) : (
                    <>
                      <Bot className="h-16 w-16 text-gray-400 mb-6" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">Ready to Generate</h3>
                      <p className="text-gray-500 max-w-md">
                        Your book structure is ready. Click "Generate Book" to start the AI writing process.
                      </p>
                    </>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Book Header */}
                  <div className="text-center border-b pb-6">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{bookMetadata.title}</h1>
                    {bookMetadata.subtitle && (
                      <h2 className="text-xl text-gray-600 mb-4">{bookMetadata.subtitle}</h2>
                    )}
                    <div className="flex justify-center gap-2 flex-wrap">
                      <Badge variant="outline">{bookMetadata.genre}</Badge>
                      <Badge variant="outline">{bookMetadata.targetAudience}</Badge>
                      <Badge variant="outline">{bookMetadata.tone}</Badge>
                    </div>
                  </div>

                  {/* Content Sections */}
                  {completedContent.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-lg flex items-center gap-2">
                          <item.icon className="h-5 w-5" />
                          {item.title}
                        </h3>
                        <div className="flex items-center gap-2">
                          {item.wordCount && (
                            <Badge variant="secondary" className="text-xs">
                              {item.wordCount.toLocaleString()} words
                            </Badge>
                          )}
                          {chapterCitations[item.id]?.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {chapterCitations[item.id].length} citations
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-gray-700 line-clamp-4">
                        {item.content?.substring(0, 300)}...
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Action Buttons */}
          {!isGenerating && completedContent.length > 0 && (
            <div className="flex gap-3 mt-6 pt-6 border-t">
              <Button className="flex-1" size="lg">
                <Download className="h-4 w-4 mr-2" />
                Export Book
              </Button>
              <Button variant="outline" className="flex-1" size="lg">
                <Edit className="h-4 w-4 mr-2" />
                Edit in Editor
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

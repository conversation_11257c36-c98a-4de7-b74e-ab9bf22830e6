import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  FileType, 
  Printer, 
  Edit, 
  BookOpen,
  FileText,
  Globe,
  Smartphone,
  Settings
} from "lucide-react";
import { GeneratedChapter, BookMetadata, BookExportOptions, Citation } from '../types';
import { documentExportService } from '../../paper-generator/document-export.service';
import { editorService } from '../../paper-generator/editor.service';
import { toast } from 'sonner';

interface BookExportDialogProps {
  bookMetadata: BookMetadata;
  generatedChapters: GeneratedChapter[];
  generatedSections: GeneratedChapter[];
  allCitations: Citation[];
  onEditInEditor: () => void;
}

export const BookExportDialog: React.FC<BookExportDialogProps> = ({
  bookMetadata,
  generatedChapters,
  generatedSections,
  allCitations,
  onEditInEditor
}) => {
  const [exportOptions, setExportOptions] = useState<BookExportOptions>({
    format: 'docx',
    includeTableOfContents: true,
    includeGlossary: true,
    includeIndex: false,
    includeBibliography: true,
    chapterNumbering: 'numeric',
    pageNumbering: true
  });
  const [isExporting, setIsExporting] = useState(false);

  const formatOptions = [
    { 
      id: 'docx', 
      name: 'Microsoft Word (.docx)', 
      icon: <FileType className="h-4 w-4" />,
      description: 'Editable document format'
    },
    { 
      id: 'pdf', 
      name: 'PDF Document (.pdf)', 
      icon: <FileText className="h-4 w-4" />,
      description: 'Print-ready format'
    },
    { 
      id: 'html', 
      name: 'Web Page (.html)', 
      icon: <Globe className="h-4 w-4" />,
      description: 'Web-friendly format'
    },
    { 
      id: 'epub', 
      name: 'E-book (.epub)', 
      icon: <Smartphone className="h-4 w-4" />,
      description: 'E-reader compatible'
    }
  ];

  const chapterNumberingOptions = [
    { id: 'numeric', name: 'Numeric (1, 2, 3...)' },
    { id: 'roman', name: 'Roman (I, II, III...)' },
    { id: 'none', name: 'No numbering' }
  ];

  const updateExportOption = <K extends keyof BookExportOptions>(
    key: K, 
    value: BookExportOptions[K]
  ) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const generateBookContent = (): string => {
    const allContent = [...generatedSections, ...generatedChapters]
      .filter(item => item.content)
      .sort((a, b) => a.order - b.order);

    let bookContent = '';

    // Title page
    bookContent += `# ${bookMetadata.title}\n\n`;
    if (bookMetadata.subtitle) {
      bookContent += `## ${bookMetadata.subtitle}\n\n`;
    }
    if (bookMetadata.authors.length > 0) {
      bookContent += `**Authors: <AUTHORS>
    }
    bookContent += `---\n\n`;

    // Table of Contents (if enabled)
    if (exportOptions.includeTableOfContents) {
      bookContent += `## Table of Contents\n\n`;
      allContent.forEach((item, index) => {
        const chapterNum = exportOptions.chapterNumbering === 'numeric' 
          ? `${index + 1}. `
          : exportOptions.chapterNumbering === 'roman' 
          ? `${toRoman(index + 1)}. `
          : '';
        bookContent += `${chapterNum}${item.title}\n`;
      });
      bookContent += `\n---\n\n`;
    }

    // Main content
    allContent.forEach((item, index) => {
      const chapterNum = exportOptions.chapterNumbering === 'numeric' 
        ? `${index + 1}. `
        : exportOptions.chapterNumbering === 'roman' 
        ? `${toRoman(index + 1)}. `
        : '';
      
      bookContent += `# ${chapterNum}${item.title}\n\n`;
      bookContent += `${item.content}\n\n`;
      
      if (item.wordCount) {
        bookContent += `*Word count: ${item.wordCount.toLocaleString()}*\n\n`;
      }
      
      bookContent += `---\n\n`;
    });

    // Bibliography (if enabled and citations exist)
    if (exportOptions.includeBibliography && allCitations.length > 0) {
      bookContent += `# Bibliography\n\n`;
      
      // Get unique citations
      const uniqueCitations = allCitations.reduce((acc, citation) => {
        const existing = acc.find(c => 
          c.authors.join(',') === citation.authors.join(',') && 
          c.year === citation.year &&
          c.title === citation.title
        );
        
        if (!existing) {
          acc.push(citation);
        }
        
        return acc;
      }, [] as Citation[]);

      uniqueCitations
        .sort((a, b) => a.authors[0]?.localeCompare(b.authors[0]) || 0)
        .forEach(citation => {
          const authors = citation.authors.length > 3 
            ? `${citation.authors[0]} et al.`
            : citation.authors.join(', ');
          
          bookContent += `${authors} (${citation.year}). ${citation.title}. ${citation.source}`;
          if (citation.doi) {
            bookContent += `. DOI: ${citation.doi}`;
          }
          bookContent += `\n\n`;
        });
    }

    return bookContent;
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const bookContent = generateBookContent();
      const fileName = `${bookMetadata.title.replace(/[^a-zA-Z0-9]/g, '_')}_book`;

      switch (exportOptions.format) {
        case 'docx':
          await documentExportService.exportMarkdownToDocx(
            bookContent, 
            bookMetadata.title, 
            fileName
          );
          break;
        case 'pdf':
          await documentExportService.exportMarkdownToPdf(
            bookContent, 
            bookMetadata.title, 
            fileName
          );
          break;
        case 'html':
          // Create HTML export
          const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${bookMetadata.title}</title>
    <style>
        body { font-family: Georgia, serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 2px solid #333; }
        h2 { color: #666; }
        .metadata { background: #f5f5f5; padding: 15px; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="metadata">
        <h1>${bookMetadata.title}</h1>
        ${bookMetadata.subtitle ? `<h2>${bookMetadata.subtitle}</h2>` : ''}
        <p><strong>Genre:</strong> ${bookMetadata.genre}</p>
        <p><strong>Target Audience:</strong> ${bookMetadata.targetAudience}</p>
        ${bookMetadata.authors.length > 0 ? `<p><strong>Authors: <AUTHORS>
    </div>
    ${bookContent.replace(/\n/g, '<br>').replace(/# /g, '<h1>').replace(/<h1>/g, '</h1><h1>').replace(/## /g, '<h2>').replace(/<h2>/g, '</h2><h2>')}
</body>
</html>`;
          
          const blob = new Blob([htmlContent], { type: 'text/html' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${fileName}.html`;
          a.click();
          URL.revokeObjectURL(url);
          break;
        case 'epub':
          // For now, export as text file with EPUB structure
          const epubContent = `EPUB Structure for: ${bookMetadata.title}\n\n${bookContent}`;
          const epubBlob = new Blob([epubContent], { type: 'text/plain' });
          const epubUrl = URL.createObjectURL(epubBlob);
          const epubLink = document.createElement('a');
          epubLink.href = epubUrl;
          epubLink.download = `${fileName}.txt`;
          epubLink.click();
          URL.revokeObjectURL(epubUrl);
          break;
      }

      toast.success(`Book exported successfully as ${exportOptions.format.toUpperCase()}!`);
    } catch (error: any) {
      console.error('Export error:', error);
      toast.error(`Export failed: ${error.message || 'Please try again'}`);
    } finally {
      setIsExporting(false);
    }
  };

  const handleEditInEditor = () => {
    const bookContent = generateBookContent();
    
    editorService.sendToMainEditor({
      title: bookMetadata.title,
      content: bookContent
    });
    
    onEditInEditor();
    toast.success("Book loaded in editor for further editing.");
  };

  // Helper function for Roman numerals
  const toRoman = (num: number): string => {
    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
    let result = '';
    
    for (let i = 0; i < values.length; i++) {
      while (num >= values[i]) {
        result += symbols[i];
        num -= values[i];
      }
    }
    
    return result;
  };

  const selectedFormat = formatOptions.find(f => f.id === exportOptions.format);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="lg" className="flex-1">
          <Download className="h-4 w-4 mr-2" />
          Export Book
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Export Book: {bookMetadata.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">Export Format</Label>
            <Select 
              value={exportOptions.format} 
              onValueChange={(value) => updateExportOption('format', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {formatOptions.map((format) => (
                  <SelectItem key={format.id} value={format.id}>
                    <div className="flex items-center gap-2">
                      {format.icon}
                      <div>
                        <div className="font-medium">{format.name}</div>
                        <div className="text-sm text-gray-500">{format.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedFormat && (
              <p className="text-sm text-gray-600">{selectedFormat.description}</p>
            )}
          </div>

          <Separator />

          {/* Book Structure Options */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Book Structure</Label>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="toc"
                  checked={exportOptions.includeTableOfContents}
                  onCheckedChange={(checked) => updateExportOption('includeTableOfContents', !!checked)}
                />
                <Label htmlFor="toc" className="text-sm">Table of Contents</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bibliography"
                  checked={exportOptions.includeBibliography}
                  onCheckedChange={(checked) => updateExportOption('includeBibliography', !!checked)}
                />
                <Label htmlFor="bibliography" className="text-sm">Bibliography</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="glossary"
                  checked={exportOptions.includeGlossary}
                  onCheckedChange={(checked) => updateExportOption('includeGlossary', !!checked)}
                />
                <Label htmlFor="glossary" className="text-sm">Glossary</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="index"
                  checked={exportOptions.includeIndex}
                  onCheckedChange={(checked) => updateExportOption('includeIndex', !!checked)}
                />
                <Label htmlFor="index" className="text-sm">Index</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Formatting Options */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Formatting</Label>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm">Chapter Numbering</Label>
                <Select 
                  value={exportOptions.chapterNumbering} 
                  onValueChange={(value) => updateExportOption('chapterNumbering', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {chapterNumberingOptions.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id="pageNumbers"
                  checked={exportOptions.pageNumbering}
                  onCheckedChange={(checked) => updateExportOption('pageNumbering', !!checked)}
                />
                <Label htmlFor="pageNumbers" className="text-sm">Page Numbers</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={handleExport}
              disabled={isExporting}
              className="flex-1"
            >
              {isExporting ? (
                <>
                  <Settings className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {selectedFormat?.name.split(' ')[0]}
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleEditInEditor}
              className="flex-1"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit in Editor
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

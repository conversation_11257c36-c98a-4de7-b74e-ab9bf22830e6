import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  BookMetadata, 
  UserInputs, 
  UserChapter, 
  GeneratedChapter, 
  ChapterOutline,
  ContentItem,
  Citation
} from './types';
import { AI_MODELS, BOOK_SECTION_TYPES } from './constants';
import { BOOK_SECTION_PROMPTS } from './prompts';
import { BookMetadataForm } from './components/BookMetadataForm';
import { ChapterCard } from './components/ChapterCard';
import { BookGenerationPanel } from './components/BookGenerationPanel';
import { AIModelSelector } from './components/AIModelSelector';
import { useBookContextStore } from './stores/book-context.store';
import bookAIService from './services/book-ai.service';
import { extractCitationsFromText } from '../paper-generator/citation-extraction.enhanced';
import { editorService } from '../paper-generator/editor.service';
import { Button } from "@/components/ui/button";
import { Plus, Book<PERSON><PERSON>, <PERSON>Left, ArrowR<PERSON> } from "lucide-react";

export function AIBookGenerator() {
  // State for user inputs
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      subtitle: "",
      genre: "",
      targetAudience: "",
      keywords: [],
      authors: [],
      description: "",
      estimatedLength: "medium",
      tone: "professional"
    },
    userChapters: []
  });

  // Track generated chapters and sections
  const [generatedChapters, setGeneratedChapters] = useState<GeneratedChapter[]>([]);
  const [generatedSections, setGeneratedSections] = useState<GeneratedChapter[]>([]);

  // UI state
  const [currentStep, setCurrentStep] = useState<'metadata' | 'chapters' | 'generation' | 'editor'>('metadata');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);

  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [chapterCitations, setChapterCitations] = useState<Record<string, string[]>>({});

  // Context store
  const {
    initializeBookContext,
    setGenerationQueue,
    startChapterGeneration,
    completeChapterGeneration,
    updateGenerationProgress,
    getContextForChapter,
    resetContext
  } = useBookContextStore();

  // Initialize generated sections based on book structure
  useEffect(() => {
    const sections = BOOK_SECTION_TYPES
      .filter(type => !type.isChapter)
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        title: type.name,
        description: type.description,
        status: 'pending' as const,
        icon: type.icon,
        order: type.order
      }));

    setGeneratedSections(sections);
  }, []);

  const handleMetadataChange = (metadata: BookMetadata) => {
    setUserInputs(prev => ({ ...prev, metadata }));
  };

  const handleNextFromMetadata = () => {
    setCurrentStep('chapters');
  };

  const addChapter = () => {
    const newChapter: UserChapter = {
      id: `chapter-${Date.now()}`,
      title: `Chapter ${userInputs.userChapters.length + 1}`,
      outline: {
        id: `outline-${Date.now()}`,
        title: `Chapter ${userInputs.userChapters.length + 1}`,
        description: '',
        subSections: [],
        estimatedWordCount: 3000,
        order: userInputs.userChapters.length + 1
      },
      items: []
    };

    setUserInputs(prev => ({
      ...prev,
      userChapters: [...prev.userChapters, newChapter]
    }));
  };

  const updateChapter = (chapterId: string, updates: Partial<UserChapter>) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  const removeChapter = (chapterId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.filter(chapter => chapter.id !== chapterId)
    }));
  };

  const moveChapter = (chapterId: string, direction: 'up' | 'down') => {
    const chapters = [...userInputs.userChapters];
    const index = chapters.findIndex(ch => ch.id === chapterId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= chapters.length) return;
    
    [chapters[index], chapters[newIndex]] = [chapters[newIndex], chapters[index]];
    
    // Update order values
    chapters.forEach((chapter, idx) => {
      chapter.outline.order = idx + 1;
    });
    
    setUserInputs(prev => ({ ...prev, userChapters: chapters }));
  };

  const handleStartGeneration = async () => {
    if (userInputs.userChapters.length === 0) {
      toast.error("Please add at least one chapter before generating");
      return;
    }

    setIsGenerating(true);
    setCurrentStep('generation');

    try {
      // Initialize context with book metadata and chapter outlines
      const chapterOutlines = userInputs.userChapters.map(ch => 
        `${ch.outline.title}: ${ch.outline.description}`
      );
      
      initializeBookContext(userInputs.metadata, chapterOutlines);

      // Create generation queue (sections + chapters)
      const sectionIds = generatedSections.map(s => s.id);
      const chapterIds = userInputs.userChapters.map(ch => ch.id);
      const allIds = [...sectionIds, ...chapterIds];
      
      setGenerationQueue(allIds);

      // Initialize generated chapters
      const initialChapters = userInputs.userChapters.map(ch => ({
        id: ch.id,
        title: ch.outline.title,
        description: ch.outline.description,
        status: 'pending' as const,
        icon: BookOpen,
        order: ch.outline.order,
        subSections: []
      }));

      setGeneratedChapters(initialChapters);

      // Start sequential generation
      await generateAllContent();

    } catch (error: any) {
      console.error('Generation initialization error:', error);
      toast.error("Failed to start generation: " + (error.message || "Please try again"));
      setIsGenerating(false);
    }
  };

  const generateAllContent = async () => {
    try {
      // Generate front matter sections first
      for (const section of generatedSections) {
        if (section.id === 'introduction' || section.id === 'preface' || section.id === 'foreword') {
          await generateSection(section.id);
        }
      }

      // Generate chapters sequentially with context
      for (const chapter of userInputs.userChapters) {
        await generateChapter(chapter);
      }

      // Generate back matter sections
      for (const section of generatedSections) {
        if (section.id === 'conclusion' || section.id === 'appendix' || section.id === 'glossary' || section.id === 'bibliography') {
          await generateSection(section.id);
        }
      }

      setIsGenerating(false);
      toast.success("Book generation completed successfully!");

    } catch (error: any) {
      console.error('Generation error:', error);
      toast.error("Generation failed: " + (error.message || "Please try again"));
      setIsGenerating(false);
    }
  };

  const generateSection = async (sectionId: string) => {
    try {
      startChapterGeneration(sectionId);
      updateGenerationProgress(sectionId, 20);

      const context = getContextForChapter(sectionId);
      let prompt = '';

      switch (sectionId) {
        case 'introduction':
          prompt = BOOK_SECTION_PROMPTS.introduction(userInputs.metadata, context);
          break;
        case 'preface':
          prompt = BOOK_SECTION_PROMPTS.preface(userInputs.metadata);
          break;
        case 'foreword':
          prompt = BOOK_SECTION_PROMPTS.foreword(userInputs.metadata);
          break;
        case 'conclusion':
          prompt = BOOK_SECTION_PROMPTS.conclusion(userInputs.metadata, context);
          break;
        default:
          prompt = `Generate the ${sectionId} section for the book "${userInputs.metadata.title}".`;
      }

      updateGenerationProgress(sectionId, 50);

      const content = await bookAIService.generateBookSection(prompt, {
        model: selectedModel,
        maxTokens: 2048,
        context
      });

      updateGenerationProgress(sectionId, 80);

      // Extract citations if applicable
      if (sectionId !== 'bibliography' && sectionId !== 'glossary') {
        const extractedInfo = extractCitationsFromText(content, sectionId);
        if (extractedInfo.citations.length > 0) {
          setAllCitations(prev => [...prev, ...extractedInfo.citations]);
          setChapterCitations(prev => ({
            ...prev,
            [sectionId]: extractedInfo.matches
          }));
        }
      }

      // Update section status
      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'completed', content } : s
      ));

      updateGenerationProgress(sectionId, 100);
      completeChapterGeneration(sectionId, { 
        id: sectionId, 
        title: sectionId, 
        description: '', 
        status: 'completed', 
        icon: BookOpen, 
        content,
        order: 0
      });

    } catch (error: any) {
      console.error(`Error generating ${sectionId}:`, error);
      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'error' } : s
      ));
      throw error;
    }
  };

  const generateChapter = async (userChapter: UserChapter) => {
    try {
      startChapterGeneration(userChapter.id);
      updateGenerationProgress(userChapter.id, 10);

      const context = getContextForChapter(userChapter.id);
      
      // Compile user content for this chapter
      const userContent = userChapter.items
        .filter(item => item.content.trim())
        .map(item => `${item.type === 'text' ? 'Text' : 'Figure'}: ${item.content}`)
        .join('\n\n');

      updateGenerationProgress(userChapter.id, 30);

      const prompt = BOOK_SECTION_PROMPTS.chapter(
        userInputs.metadata,
        userChapter.outline,
        context,
        userContent
      );

      updateGenerationProgress(userChapter.id, 50);

      const content = await bookAIService.generateChapter(prompt, {
        model: selectedModel,
        context
      });

      updateGenerationProgress(userChapter.id, 70);

      // Generate chapter summary for context
      const { summary, keyPoints } = await bookAIService.generateChapterSummary(
        userChapter.outline.title,
        content,
        { model: selectedModel }
      );

      updateGenerationProgress(userChapter.id, 85);

      // Extract citations
      const extractedInfo = extractCitationsFromText(content, userChapter.id);
      if (extractedInfo.citations.length > 0) {
        setAllCitations(prev => [...prev, ...extractedInfo.citations]);
        setChapterCitations(prev => ({
          ...prev,
          [userChapter.id]: extractedInfo.matches
        }));
      }

      // Update chapter status
      const generatedChapter: GeneratedChapter = {
        id: userChapter.id,
        title: userChapter.outline.title,
        description: userChapter.outline.description,
        status: 'completed',
        icon: BookOpen,
        content,
        summary,
        wordCount: content.split(' ').length,
        citations: extractedInfo.matches,
        order: userChapter.outline.order
      };

      setGeneratedChapters(prev => prev.map(ch =>
        ch.id === userChapter.id ? generatedChapter : ch
      ));

      updateGenerationProgress(userChapter.id, 100);
      completeChapterGeneration(userChapter.id, generatedChapter);

    } catch (error: any) {
      console.error(`Error generating chapter ${userChapter.id}:`, error);
      setGeneratedChapters(prev => prev.map(ch =>
        ch.id === userChapter.id ? { ...ch, status: 'error' } : ch
      ));
      throw error;
    }
  };

  const canProceedToGeneration = () => {
    return userInputs.metadata.title.trim() !== '' &&
           userInputs.metadata.genre !== '' &&
           userInputs.metadata.targetAudience !== '' &&
           userInputs.userChapters.length > 0;
  };

  const handleEditInEditor = () => {
    // Generate complete book content
    const allContent = [...generatedSections, ...generatedChapters]
      .filter(item => item.content)
      .sort((a, b) => a.order - b.order);

    let bookContent = '';

    // Title page
    bookContent += `# ${userInputs.metadata.title}\n\n`;
    if (userInputs.metadata.subtitle) {
      bookContent += `## ${userInputs.metadata.subtitle}\n\n`;
    }
    if (userInputs.metadata.authors.length > 0) {
      bookContent += `**Authors: <AUTHORS>
    }
    bookContent += `---\n\n`;

    // Main content
    allContent.forEach((item, index) => {
      bookContent += `# ${item.title}\n\n`;
      bookContent += `${item.content}\n\n`;

      if (item.wordCount) {
        bookContent += `*Word count: ${item.wordCount.toLocaleString()}*\n\n`;
      }

      bookContent += `---\n\n`;
    });

    // Send to editor
    editorService.sendToMainEditor({
      title: userInputs.metadata.title,
      content: bookContent
    });

    toast.success("Book loaded in editor for further editing.");
  };

  // Render based on current step
  if (currentStep === 'metadata') {
    return (
      <BookMetadataForm
        metadata={userInputs.metadata}
        onMetadataChange={handleMetadataChange}
        onNext={handleNextFromMetadata}
      />
    );
  }

  if (currentStep === 'chapters') {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
            <BookOpen className="h-10 w-10 text-blue-600" />
            Define Your Chapters
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create detailed chapter outlines and provide any content you want the AI to incorporate.
          </p>
        </div>

        {/* AI Model Selector */}
        <AIModelSelector
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          models={AI_MODELS}
        />

        {/* Chapter Cards */}
        <div className="space-y-6">
          {userInputs.userChapters.map((chapter, index) => (
            <ChapterCard
              key={chapter.id}
              chapter={chapter}
              chapterIndex={index}
              onChapterUpdate={updateChapter}
              onChapterRemove={removeChapter}
              onMoveChapter={moveChapter}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
            />
          ))}

          {/* Add Chapter Button */}
          <div className="text-center">
            <Button onClick={addChapter} size="lg" variant="outline">
              <Plus className="h-5 w-5 mr-2" />
              Add Chapter
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button 
            onClick={() => setCurrentStep('metadata')}
            variant="outline"
            size="lg"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Metadata
          </Button>
          
          <Button 
            onClick={handleStartGeneration}
            disabled={!canProceedToGeneration()}
            size="lg"
            className="px-8"
          >
            Generate Book
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    );
  }

  if (currentStep === 'generation') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {isGenerating ? "Generating Your Book" : "Book Generated"}
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {isGenerating 
                ? "Our AI is carefully crafting each chapter and section of your book with context awareness" 
                : "All chapters and sections of your book have been generated successfully"}
            </p>
          </div>

          <BookGenerationPanel
            generatedChapters={generatedChapters}
            generatedSections={generatedSections}
            isGenerating={isGenerating}
            bookMetadata={userInputs.metadata}
            allCitations={allCitations}
            chapterCitations={chapterCitations}
            onEditInEditor={handleEditInEditor}
          />
        </div>
      </div>
    );
  }

  return null;
}

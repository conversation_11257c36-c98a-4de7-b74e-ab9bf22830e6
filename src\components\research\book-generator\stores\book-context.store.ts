import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  BookContext, 
  ChapterContext, 
  GeneratedChapter, 
  BookMetadata 
} from '../types';
import { CONTEXT_SETTINGS } from '../constants';

interface BookContextState {
  // Core context data
  bookContext: BookContext | null;
  chapterContexts: ChapterContext[];
  
  // Generation queue and status
  generationQueue: string[]; // Chapter IDs in generation order
  currentlyGenerating: string | null;
  generationProgress: Record<string, number>; // Chapter ID -> progress percentage
  
  // Context management
  contextTokenCount: number;
  maxContextTokens: number;
  
  // Actions
  initializeBookContext: (metadata: BookMetadata, chapterOutlines: string[]) => void;
  addChapterContext: (context: ChapterContext) => void;
  updateChapterContext: (chapterId: string, updates: Partial<ChapterContext>) => void;
  removeOldestContext: () => void;
  
  // Generation queue management
  setGenerationQueue: (chapterIds: string[]) => void;
  startChapterGeneration: (chapterId: string) => void;
  completeChapterGeneration: (chapterId: string, chapter: GeneratedChapter) => void;
  updateGenerationProgress: (chapterId: string, progress: number) => void;
  
  // Context optimization
  optimizeContext: () => void;
  getContextForChapter: (chapterId: string) => BookContext;
  calculateTokenCount: (text: string) => number;
  
  // Reset and cleanup
  resetContext: () => void;
  clearCompletedChapters: () => void;
}

export const useBookContextStore = create<BookContextState>()(
  devtools(
    (set, get) => ({
      // Initial state
      bookContext: null,
      chapterContexts: [],
      generationQueue: [],
      currentlyGenerating: null,
      generationProgress: {},
      contextTokenCount: 0,
      maxContextTokens: CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS,

      // Initialize book context with metadata and outline
      initializeBookContext: (metadata: BookMetadata, chapterOutlines: string[]) => {
        const bookOutline = `
Book: ${metadata.title}
${metadata.subtitle ? `Subtitle: ${metadata.subtitle}` : ''}
Genre: ${metadata.genre}
Target Audience: ${metadata.targetAudience}
Tone: ${metadata.tone}
Description: ${metadata.description}

Chapter Structure:
${chapterOutlines.map((outline, index) => `${index + 1}. ${outline}`).join('\n')}
        `.trim();

        const newContext: BookContext = {
          bookOutline,
          previousChapters: [],
          currentChapter: '',
          totalWordCount: 0,
          maxContextTokens: CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS
        };

        set({
          bookContext: newContext,
          contextTokenCount: get().calculateTokenCount(bookOutline),
          chapterContexts: [],
          generationQueue: [],
          currentlyGenerating: null,
          generationProgress: {}
        });
      },

      // Add a new chapter context
      addChapterContext: (context: ChapterContext) => {
        const state = get();
        const newContexts = [...state.chapterContexts, context];
        
        // Keep only the most recent chapters to manage context size
        const trimmedContexts = newContexts.slice(-CONTEXT_SETTINGS.MAX_PREVIOUS_CHAPTERS);
        
        // Update total word count
        const totalWordCount = trimmedContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
        
        // Update book context
        const updatedBookContext = state.bookContext ? {
          ...state.bookContext,
          previousChapters: trimmedContexts,
          totalWordCount
        } : null;

        set({
          chapterContexts: trimmedContexts,
          bookContext: updatedBookContext,
          contextTokenCount: state.calculateTokenCount(
            (updatedBookContext?.bookOutline || '') + 
            trimmedContexts.map(ctx => ctx.summary).join('\n')
          )
        });
      },

      // Update existing chapter context
      updateChapterContext: (chapterId: string, updates: Partial<ChapterContext>) => {
        const state = get();
        const updatedContexts = state.chapterContexts.map(ctx =>
          ctx.chapterId === chapterId ? { ...ctx, ...updates } : ctx
        );

        const totalWordCount = updatedContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
        
        const updatedBookContext = state.bookContext ? {
          ...state.bookContext,
          previousChapters: updatedContexts,
          totalWordCount
        } : null;

        set({
          chapterContexts: updatedContexts,
          bookContext: updatedBookContext
        });
      },

      // Remove oldest context to free up space
      removeOldestContext: () => {
        const state = get();
        if (state.chapterContexts.length > 0) {
          const newContexts = state.chapterContexts.slice(1);
          const totalWordCount = newContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
          
          const updatedBookContext = state.bookContext ? {
            ...state.bookContext,
            previousChapters: newContexts,
            totalWordCount
          } : null;

          set({
            chapterContexts: newContexts,
            bookContext: updatedBookContext,
            contextTokenCount: state.calculateTokenCount(
              (updatedBookContext?.bookOutline || '') + 
              newContexts.map(ctx => ctx.summary).join('\n')
            )
          });
        }
      },

      // Set the generation queue
      setGenerationQueue: (chapterIds: string[]) => {
        set({
          generationQueue: chapterIds,
          generationProgress: chapterIds.reduce((acc, id) => ({ ...acc, [id]: 0 }), {})
        });
      },

      // Start generating a chapter
      startChapterGeneration: (chapterId: string) => {
        set({
          currentlyGenerating: chapterId,
          generationProgress: {
            ...get().generationProgress,
            [chapterId]: 10 // Initial progress
          }
        });
      },

      // Complete chapter generation and add to context
      completeChapterGeneration: (chapterId: string, chapter: GeneratedChapter) => {
        const state = get();
        
        // Create chapter context from generated chapter
        if (chapter.summary) {
          const chapterContext: ChapterContext = {
            chapterId: chapter.id,
            summary: chapter.summary,
            keyPoints: [], // Will be extracted from summary
            wordCount: chapter.wordCount || 0,
            generatedAt: new Date()
          };

          state.addChapterContext(chapterContext);
        }

        // Update generation status
        const updatedQueue = state.generationQueue.filter(id => id !== chapterId);
        const updatedProgress = { ...state.generationProgress };
        updatedProgress[chapterId] = 100;

        set({
          currentlyGenerating: updatedQueue.length > 0 ? updatedQueue[0] : null,
          generationQueue: updatedQueue,
          generationProgress: updatedProgress
        });
      },

      // Update generation progress
      updateGenerationProgress: (chapterId: string, progress: number) => {
        set({
          generationProgress: {
            ...get().generationProgress,
            [chapterId]: Math.min(100, Math.max(0, progress))
          }
        });
      },

      // Optimize context by removing old chapters if needed
      optimizeContext: () => {
        const state = get();
        
        while (state.contextTokenCount > state.maxContextTokens && state.chapterContexts.length > 1) {
          state.removeOldestContext();
        }
      },

      // Get context for a specific chapter generation
      getContextForChapter: (chapterId: string): BookContext => {
        const state = get();
        
        if (!state.bookContext) {
          throw new Error('Book context not initialized');
        }

        // Optimize context before returning
        state.optimizeContext();

        return {
          ...state.bookContext,
          currentChapter: chapterId,
          previousChapters: state.chapterContexts
        };
      },

      // Calculate approximate token count (rough estimation: 1 token ≈ 4 characters)
      calculateTokenCount: (text: string): number => {
        return Math.ceil(text.length / 4);
      },

      // Reset all context
      resetContext: () => {
        set({
          bookContext: null,
          chapterContexts: [],
          generationQueue: [],
          currentlyGenerating: null,
          generationProgress: {},
          contextTokenCount: 0
        });
      },

      // Clear completed chapters from queue
      clearCompletedChapters: () => {
        const state = get();
        const incompleteChapters = state.generationQueue.filter(
          id => state.generationProgress[id] < 100
        );
        
        set({
          generationQueue: incompleteChapters,
          currentlyGenerating: incompleteChapters.length > 0 ? incompleteChapters[0] : null
        });
      }
    }),
    {
      name: 'book-context-store',
      partialize: (state) => ({
        // Only persist essential data, not the entire state
        bookContext: state.bookContext,
        chapterContexts: state.chapterContexts,
        generationProgress: state.generationProgress
      })
    }
  )
);

import { AIGenerationOptions, AIAnalysisResult, BookContext, ChapterContext, GeneratedOutline, BookMetadata, UserChapter } from '../types';
import { CONTEXT_SETTINGS } from '../constants';
import { OUTLINE_GENERATION_PROMPTS } from '../prompts';

/**
 * Service for handling AI book generation with context management
 * Extends the paper AI service with book-specific capabilities
 */
export class BookAIService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }
  
  /**
   * Generate a book chapter with context awareness
   */
  async generateChapter(
    chapterPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { 
        model = "anthropic/claude-3.5-sonnet", 
        maxTokens = CONTEXT_SETTINGS.CHAPTER_GENERATION_TOKENS,
        context
      } = options;
      
      // Build context-aware system prompt
      const systemPrompt = this.buildSystemPrompt(context);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: chapterPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7 // Slightly creative for book writing
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No content generated]';
    } catch (error: any) {
      console.error('AI Chapter Generation Error:', error);
      throw new Error('Failed to generate chapter. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate comprehensive outlines for all chapters
   */
  async generateAllChapterOutlines(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    try {
      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 4096
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(metadata, userChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book outline generator. Create comprehensive, detailed outlines that provide clear structure for book chapters. Always respond with valid JSON."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      // Parse the JSON response
      try {
        const parsed = JSON.parse(content);
        const outlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => ({
          id: `outline-${userChapters[index]?.id || index}`,
          chapterId: userChapters[index]?.id || `chapter-${index}`,
          title: chapter.title,
          description: chapter.description,
          sections: chapter.sections.map((section: any) => ({
            id: `section-${section.order}`,
            title: section.title,
            description: section.description,
            level: section.level,
            order: section.order,
            estimatedWordCount: section.estimatedWordCount,
            keyPoints: section.keyPoints || []
          })),
          estimatedWordCount: chapter.estimatedWordCount,
          keyPoints: chapter.keyPoints || [],
          status: 'ready' as const
        }));

        return outlines;
      } catch (parseError) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error('Failed to parse generated outlines. Please try again.');
      }

    } catch (error: any) {
      console.error('AI Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outlines. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate outline for a single chapter
   */
  async generateChapterOutline(
    metadata: BookMetadata,
    userChapter: UserChapter,
    previousChapters: string[] = [],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline> {
    try {
      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 2048
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateChapterOutline(metadata, userChapter, previousChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book outline generator. Create detailed, comprehensive outlines for book chapters. Always respond with valid JSON."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      // Parse the JSON response
      try {
        const parsed = JSON.parse(content);

        const outline: GeneratedOutline = {
          id: `outline-${userChapter.id}`,
          chapterId: userChapter.id,
          title: parsed.title,
          description: parsed.description,
          sections: parsed.sections.map((section: any) => ({
            id: `section-${section.order}`,
            title: section.title,
            description: section.description,
            level: section.level,
            order: section.order,
            estimatedWordCount: section.estimatedWordCount,
            keyPoints: section.keyPoints || []
          })),
          estimatedWordCount: parsed.estimatedWordCount,
          keyPoints: parsed.keyPoints || [],
          status: 'ready' as const
        };

        return outline;
      } catch (parseError) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error('Failed to parse generated outline. Please try again.');
      }

    } catch (error: any) {
      console.error('AI Chapter Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outline. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate a chapter summary for context management
   */
  async generateChapterSummary(
    chapterTitle: string,
    chapterContent: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<{ summary: string; keyPoints: string[] }> {
    try {
      const { 
        model = "anthropic/claude-3.5-sonnet", 
        maxTokens = CONTEXT_SETTINGS.SUMMARY_GENERATION_TOKENS 
      } = options;
      
      const summaryPrompt = `Create a concise summary of the following chapter for maintaining narrative context in subsequent chapters.

Chapter Title: ${chapterTitle}

Chapter Content:
${chapterContent}

Requirements:
- Maximum 400 words
- Focus on key concepts, main arguments, and important conclusions
- Extract 3-5 bullet points of the most important takeaways
- Maintain essential information for narrative continuity
- Use clear, concise language

Format your response as:
SUMMARY: [Your summary here]

KEY_POINTS:
• [Point 1]
• [Point 2]
• [Point 3]
• [Point 4]
• [Point 5]`;

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert at creating concise, informative summaries that preserve essential narrative context."
            },
            {
              role: "user",
              content: summaryPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.3 // Lower temperature for consistent summaries
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      // Parse the response to extract summary and key points
      const summaryMatch = content.match(/SUMMARY:\s*(.*?)(?=KEY_POINTS:|$)/s);
      const keyPointsMatch = content.match(/KEY_POINTS:\s*(.*)/s);
      
      const summary = summaryMatch ? summaryMatch[1].trim() : content;
      const keyPointsText = keyPointsMatch ? keyPointsMatch[1].trim() : '';
      const keyPoints = keyPointsText
        .split('\n')
        .filter(line => line.trim().startsWith('•'))
        .map(line => line.replace('•', '').trim())
        .filter(point => point.length > 0);
      
      return { summary, keyPoints };
    } catch (error: any) {
      console.error('AI Summary Generation Error:', error);
      throw new Error('Failed to generate chapter summary. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate book section (preface, introduction, conclusion, etc.)
   */
  async generateBookSection(
    sectionPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { 
        model = "anthropic/claude-3.5-sonnet", 
        maxTokens = 2048,
        context
      } = options;
      
      const systemPrompt = context 
        ? this.buildSystemPrompt(context)
        : "You are an expert book writer who creates compelling, well-structured content that engages readers and maintains consistent tone and style throughout.";
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: sectionPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No content generated]';
    } catch (error: any) {
      console.error('AI Section Generation Error:', error);
      throw new Error('Failed to generate book section. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Analyze text or image content for book chapters
   */
  async analyzeContent(
    prompt: string, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No analysis available]';
    } catch (error: any) {
      console.error('AI Content Analysis Error:', error);
      throw new Error('Failed to analyze content. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Analyze an image for book content
   */
  async analyzeImage(
    imageUrl: string, 
    prompt: string, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;
      
      const dataUrl = await this.convertImageToBase64(imageUrl);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: prompt },
                { type: "image_url", image_url: { url: dataUrl } }
              ]
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No analysis available]';
    } catch (error: any) {
      console.error('AI Image Analysis Error:', error);
      throw new Error('Failed to analyze image. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Build context-aware system prompt
   */
  private buildSystemPrompt(context?: BookContext): string {
    if (!context) {
      return "You are an expert book writer who creates compelling, well-structured content that engages readers and maintains consistent tone and style throughout.";
    }

    const contextInfo = context.previousChapters.length > 0 
      ? `
Previous chapters context:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
  Summary: ${ch.summary}
  Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}

Current book progress: ${context.totalWordCount} words written.
` 
      : '';

    return `You are an expert book writer creating content for a book project. 

Book Overview:
${context.bookOutline}

${contextInfo}

Your task is to write content that:
- Maintains consistency with the established narrative and tone
- Builds upon previous chapters naturally
- Provides substantial, book-quality content
- Engages the target audience effectively
- Uses proper academic citations where appropriate
- Follows the book's overall structure and objectives

Write in a professional, engaging style that matches the book's intended tone and audience.`;
  }

  /**
   * Convert image to base64 for API calls
   */
  private async convertImageToBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to base64:', error);
      throw new Error('Failed to process image');
    }
  }
}

// Export singleton instance
const bookAIService = new BookAIService();
export default bookAIService;
